import React, { useEffect, useState } from "react";
import { format } from "date-fns";
import { IncidentFormValues } from "@/utils/validationSchema";
import { AlertTriangle } from "lucide-react";
import {
  incidentTypes,
  countries,
  cities,
  businessUnits,
  projectOptions,
  incidentReviewers
} from "@/utils/formData";

interface IncidentPreviewProps {
  data: IncidentFormValues;
}

const IncidentPreview: React.FC<IncidentPreviewProps> = ({ data }) => {
  const [incidentLevel, setIncidentLevel] = useState<string>("");

  // Helper function to get label from value
  const getLabelFromValue = (value: string, options: { value: string; label: string }[]) => {
    const option = options.find(opt => opt.value === value);
    return option ? option.label : value;
  };

  // Get city options based on country
  const getCityLabel = (countryValue: string, cityValue: string) => {
    if (!countryValue || !cityValue) return "";

    const countryKey = countryValue as keyof typeof cities;
    const cityOptions = cities[countryKey] || cities.other;
    return getLabelFromValue(cityValue, cityOptions);
  };

  // Function to get the appropriate color class based on incident level
  const getClassificationColor = (level: string) => {
    if (level.includes("Level 5")) {
      return "bg-red-50 border-red-200 text-red-800";
    } else if (level.includes("Level 4")) {
      return "bg-orange-50 border-orange-200 text-orange-800";
    } else if (level.includes("Level 3")) {
      return "bg-amber-50 border-amber-200 text-amber-800";
    } else if (level.includes("Level 2")) {
      return "bg-yellow-50 border-yellow-200 text-yellow-800";
    } else if (level.includes("Level 1")) {
      return "bg-green-50 border-green-200 text-green-800";
    } else {
      return "bg-blue-50 border-blue-200 text-blue-700"; // Near Miss
    }
  };

  // Function to calculate incident level
  const calculateIncidentLevel = () => {
    let level = "";

    // Check each classification level and determine the highest level selected
    if (data.injuryClassification.isFatality === true) {
      level = "Level 5 – Critical Incident";
    } else if (data.injuryClassification.isPermanentDisability === true) {
      level = "Level 4 – High Severity Incident";
    } else if (data.injuryClassification.isLostTimeIncident === true) {
      level = "Level 3 – Medium Severity Incident (LTI)";
    } else if (data.injuryClassification.isMedicalTreatment === true) {
      level = "Level 2 – Low Severity Incident (MTI)";
    } else if (data.injuryClassification.isFirstAid === true) {
      level = "Level 1 – Very Low Severity Incident (FAI)";
    } else if (
      data.injuryClassification.isFatality === false &&
      data.injuryClassification.isPermanentDisability === false &&
      data.injuryClassification.isLostTimeIncident === false &&
      data.injuryClassification.isMedicalTreatment === false &&
      data.injuryClassification.isFirstAid === false
    ) {
      // Only set to Near Miss if all questions are explicitly answered as No
      level = "Near Miss";
    }

    setIncidentLevel(level);
  };

  // Calculate incident level when component mounts or data changes
  useEffect(() => {
    calculateIncidentLevel();
  }, [
    data.injuryClassification.isFatality,
    data.injuryClassification.isPermanentDisability,
    data.injuryClassification.isLostTimeIncident,
    data.injuryClassification.isMedicalTreatment,
    data.injuryClassification.isFirstAid
  ]);

  return (
    <div className="space-y-6 rounded-lg shadow-sm overflow-hidden">
      <div className="flex items-center justify-between bg-primary/10 p-4 border-b">
        <h3 className="font-semibold text-lg text-primary flex items-center gap-2">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-clipboard-list"><rect width="8" height="4" x="8" y="2" rx="1" ry="1"/><path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"/><path d="M12 11h4"/><path d="M12 16h4"/><path d="M8 11h.01"/><path d="M8 16h.01"/></svg>
          Incident Report Preview
        </h3>
        <p className="text-sm text-primary font-medium">Please review all details before submission</p>
      </div>
      <div className="px-6 pt-2 pb-6">

      {/* Basic Information Section */}
      <div className="space-y-4 mb-6">
        <div className="flex items-center gap-2">
          <div className="p-1.5 rounded-full bg-blue-100 text-blue-600">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-info"><circle cx="12" cy="12" r="10"/><path d="M12 16v-4"/><path d="M12 8h.01"/></svg>
          </div>
          <h4 className="font-semibold text-blue-600 text-base">Basic Information</h4>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-gray-50 p-4 rounded-md">
            <p className="text-xs uppercase tracking-wider text-gray-500 mb-1 font-medium">Incident Title</p>
            <p className="font-medium text-gray-900">{data.incidentTitle || "Not specified"}</p>
          </div>
          <div className="bg-gray-50 p-4 rounded-md">
            <p className="text-xs uppercase tracking-wider text-gray-500 mb-1 font-medium">Incident Type</p>
            <p className="font-medium text-gray-900">
              {data.incidentType ? getLabelFromValue(data.incidentType, incidentTypes) : "Not specified"}
            </p>
          </div>
          <div className="bg-gray-50 p-4 rounded-md">
            <p className="text-xs uppercase tracking-wider text-gray-500 mb-1 font-medium">Incident Date</p>
            <p className="font-medium text-gray-900">{format(data.incidentDate, "PPP")}</p>
          </div>
          <div className="bg-gray-50 p-4 rounded-md">
            <p className="text-xs uppercase tracking-wider text-gray-500 mb-1 font-medium">Incident Time</p>
            <p className="font-medium text-gray-900">{data.incidentTime}</p>
          </div>
        </div>
        <div className="bg-gray-50 p-4 rounded-md mt-2">
          <p className="text-xs uppercase tracking-wider text-gray-500 mb-2 font-medium">Description</p>
          <p className="font-medium text-gray-900 whitespace-pre-wrap">{data.description || "Not specified"}</p>
        </div>
      </div>

      {/* Location Section */}
      <div className="space-y-4 mb-6">
        <div className="flex items-center gap-2">
          <div className="p-1.5 rounded-full bg-green-100 text-green-600">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-map-pin"><path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"/><circle cx="12" cy="10" r="3"/></svg>
          </div>
          <h4 className="font-semibold text-green-600 text-base">Location</h4>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-gray-50 p-4 rounded-md">
            <p className="text-xs uppercase tracking-wider text-gray-500 mb-1 font-medium">Country</p>
            <p className="font-medium text-gray-900">
              {data.locationCountry ? getLabelFromValue(data.locationCountry, countries) : "Not specified"}
            </p>
          </div>
          <div className="bg-gray-50 p-4 rounded-md">
            <p className="text-xs uppercase tracking-wider text-gray-500 mb-1 font-medium">City</p>
            <p className="font-medium text-gray-900">
              {data.locationCity ? getCityLabel(data.locationCountry, data.locationCity) : "Not specified"}
            </p>
          </div>
          <div className="bg-gray-50 p-4 rounded-md">
            <p className="text-xs uppercase tracking-wider text-gray-500 mb-1 font-medium">Business Unit</p>
            <p className="font-medium text-gray-900">
              {data.locationBusinessUnit ? getLabelFromValue(data.locationBusinessUnit, businessUnits) : "Not specified"}
            </p>
          </div>
          <div className="bg-gray-50 p-4 rounded-md">
            <p className="text-xs uppercase tracking-wider text-gray-500 mb-1 font-medium">Project/DC Ops</p>
            <p className="font-medium text-gray-900">
              {data.locationProject ? getLabelFromValue(data.locationProject, projectOptions) : "Not specified"}
            </p>
          </div>
        </div>
        <div className="bg-gray-50 p-4 rounded-md mt-2">
          <p className="text-xs uppercase tracking-wider text-gray-500 mb-1 font-medium">Level and Location Details</p>
          <p className="font-medium text-gray-900">{data.locationDetails || "Not specified"}</p>
        </div>
      </div>

      {/* Injury Classification Section */}
      <div className="space-y-4 mb-6">
        <div className="flex items-center gap-2">
          <div className="p-1.5 rounded-full bg-amber-100 text-amber-600">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-alert-triangle"><path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"/><path d="M12 9v4"/><path d="M12 17h.01"/></svg>
          </div>
          <h4 className="font-semibold text-amber-600 text-base">Injury Classification</h4>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-gray-50 p-4 rounded-md flex flex-col">
            <p className="text-xs uppercase tracking-wider text-gray-500 mb-1 font-medium">Work Related?</p>
            <p className="font-medium text-gray-900">
              {data.isWorkRelated === null ? "Not specified" : (
                <span className={data.isWorkRelated ? "text-green-600" : "text-gray-600"}>
                  {data.isWorkRelated ? "Yes" : "No"}
                </span>
              )}
            </p>
          </div>
          <div className="bg-gray-50 p-4 rounded-md flex flex-col">
            <p className="text-xs uppercase tracking-wider text-gray-500 mb-1 font-medium">Loss of Consciousness?</p>
            <p className="font-medium text-gray-900">
              {data.lossOfConsciousness === null ? "Not specified" : (
                <span className={data.lossOfConsciousness ? "text-amber-600" : "text-gray-600"}>
                  {data.lossOfConsciousness ? "Yes" : "No"}
                </span>
              )}
            </p>
          </div>
          <div className="bg-gray-50 p-4 rounded-md flex flex-col">
            <p className="text-xs uppercase tracking-wider text-gray-500 mb-1 font-medium">Dangerous Occurrence?</p>
            <p className="font-medium text-gray-900">
              {data.isDangerousOccurrence === null ? "Not specified" : (
                <span className={data.isDangerousOccurrence ? "text-red-600" : "text-gray-600"}>
                  {data.isDangerousOccurrence ? "Yes" : "No"}
                </span>
              )}
            </p>
          </div>
        </div>

        <div className="mt-4 bg-gray-50 p-4 rounded-md">
          <p className="text-xs uppercase tracking-wider text-gray-500 mb-3 font-medium">Did this incident result in:</p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center gap-2">
              <div className={`w-4 h-4 rounded-full flex-shrink-0 ${data.injuryClassification.isFatality ? 'bg-red-500' : 'bg-gray-200'}`}></div>
              <div>
                <p className="text-sm font-medium">Fatality</p>
                <p className="text-xs text-gray-500">{data.injuryClassification.isFatality ? "Yes" : "No"}</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <div className={`w-4 h-4 rounded-full flex-shrink-0 ${data.injuryClassification.isPermanentDisability ? 'bg-red-500' : 'bg-gray-200'}`}></div>
              <div>
                <p className="text-sm font-medium">Permanent Disability</p>
                <p className="text-xs text-gray-500">{data.injuryClassification.isPermanentDisability ? "Yes" : "No"}</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <div className={`w-4 h-4 rounded-full flex-shrink-0 ${data.injuryClassification.isLostTimeIncident ? 'bg-amber-500' : 'bg-gray-200'}`}></div>
              <div>
                <p className="text-sm font-medium">Lost Time Incident</p>
                <p className="text-xs text-gray-500">{data.injuryClassification.isLostTimeIncident ? "Yes" : "No"}</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <div className={`w-4 h-4 rounded-full flex-shrink-0 ${data.injuryClassification.isMedicalTreatment ? 'bg-amber-400' : 'bg-gray-200'}`}></div>
              <div>
                <p className="text-sm font-medium">Medical Treatment</p>
                <p className="text-xs text-gray-500">{data.injuryClassification.isMedicalTreatment ? "Yes" : "No"}</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <div className={`w-4 h-4 rounded-full flex-shrink-0 ${data.injuryClassification.isFirstAid ? 'bg-green-500' : 'bg-gray-200'}`}></div>
              <div>
                <p className="text-sm font-medium">First Aid</p>
                <p className="text-xs text-gray-500">{data.injuryClassification.isFirstAid ? "Yes" : "No"}</p>
              </div>
            </div>
          </div>

          {/* Incident Classification Result */}
          {incidentLevel && (
            <div className={`mt-6 p-4 rounded-md border ${getClassificationColor(incidentLevel)}`}>
              <h3 className="font-bold text-base flex items-center">
                <AlertTriangle className="mr-2 h-5 w-5" />
                Incident Classification Result
              </h3>
              <p className="mt-2 font-medium">
                Final Classification: {incidentLevel}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Incident Reviewer Section */}
      <div className="space-y-4 mb-6">
        <div className="flex items-center gap-2">
          <div className="p-1.5 rounded-full bg-purple-100 text-purple-600">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-user"><path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"/><circle cx="12" cy="7" r="4"/></svg>
          </div>
          <h4 className="font-semibold text-purple-600 text-base">Incident Reviewer</h4>
        </div>
        <div className="bg-gray-50 p-4 rounded-md flex items-center gap-3">
          <div className="bg-purple-100 text-purple-600 p-2 rounded-full">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-user-check"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><polyline points="16 11 18 13 22 9"/></svg>
          </div>
          <div>
            <p className="text-xs uppercase tracking-wider text-gray-500 mb-1 font-medium">Assigned Reviewer</p>
            <p className="font-medium text-gray-900">
              {data.incidentReviewer ? getLabelFromValue(data.incidentReviewer, incidentReviewers) : "Not specified"}
            </p>
          </div>
        </div>
      </div>
    </div>
    </div>
  );
};

export default IncidentPreview;
