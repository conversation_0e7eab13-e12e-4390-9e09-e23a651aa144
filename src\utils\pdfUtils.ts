import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { Incident } from '@/contexts/IncidentContext';
import { format } from 'date-fns';

/**
 * Generates a PDF from an HTML element
 * @param element The HTML element to convert to PDF
 * @param filename The name of the PDF file
 */
export const generatePdfFromElement = async (
  element: HTMLElement,
  filename: string = 'incident-report.pdf'
): Promise<void> => {
  try {
    // Create a new jsPDF instance (A4 size, portrait)
    const pdf = new jsPDF('p', 'mm', 'a4');
    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();
    
    // Convert the HTML element to a canvas
    const canvas = await html2canvas(element, {
      scale: 2, // Higher scale for better quality
      useCORS: true, // Enable CORS for images
      logging: false, // Disable logging
      backgroundColor: '#ffffff', // White background
    });
    
    // Get the canvas as an image
    const imgData = canvas.toDataURL('image/png');
    
    // Calculate the number of pages needed
    const imgHeight = (canvas.height * pageWidth) / canvas.width;
    let heightLeft = imgHeight;
    let position = 0;
    let page = 1;
    
    // Add the first page
    pdf.addImage(imgData, 'PNG', 0, position, pageWidth, imgHeight);
    heightLeft -= pageHeight;
    
    // Add additional pages if needed
    while (heightLeft > 0) {
      position = heightLeft - imgHeight;
      pdf.addPage();
      pdf.addImage(imgData, 'PNG', 0, position, pageWidth, imgHeight);
      heightLeft -= pageHeight;
      page++;
    }
    
    // Add page numbers
    for (let i = 1; i <= page; i++) {
      pdf.setPage(i);
      pdf.setFontSize(8);
      pdf.setTextColor(100, 100, 100);
      pdf.text(`Page ${i} of ${page}`, pageWidth - 25, pageHeight - 10);
    }
    
    // Save the PDF
    pdf.save(filename);
  } catch (error) {
    console.error('Error generating PDF:', error);
    throw error;
  }
};

/**
 * Formats an incident date for display
 * @param date The date to format
 * @returns Formatted date string
 */
export const formatIncidentDate = (date: Date): string => {
  return format(date, 'PPP');
};

/**
 * Gets the severity level label based on injury classification
 * @param incident The incident to get the severity level for
 * @returns Severity level label
 */
export const getSeverityLevel = (incident: Incident): string => {
  const { injuryClassification } = incident;
  
  if (!injuryClassification) return 'Not Classified';
  
  if (injuryClassification.isFatality) {
    return 'Level 5 – Critical Incident';
  } else if (injuryClassification.isPermanentDisability) {
    return 'Level 4 – High Severity Incident';
  } else if (injuryClassification.isLostTimeIncident) {
    return 'Level 3 – Medium Severity Incident (LTI)';
  } else if (injuryClassification.isMedicalTreatment) {
    return 'Level 2 – Low Severity Incident (MTI)';
  } else if (injuryClassification.isFirstAid) {
    return 'Level 1 – Very Low Severity Incident (FAI)';
  } else {
    return 'Near Miss';
  }
};

/**
 * Gets the severity level color class based on the severity level
 * @param level The severity level
 * @returns CSS color class
 */
export const getSeverityColorClass = (level: string): string => {
  if (level.includes('Level 5')) {
    return 'bg-red-50 border-red-200 text-red-800';
  } else if (level.includes('Level 4')) {
    return 'bg-orange-50 border-orange-200 text-orange-800';
  } else if (level.includes('Level 3')) {
    return 'bg-amber-50 border-amber-200 text-amber-800';
  } else if (level.includes('Level 2')) {
    return 'bg-yellow-50 border-yellow-200 text-yellow-800';
  } else if (level.includes('Level 1')) {
    return 'bg-green-50 border-green-200 text-green-800';
  } else {
    return 'bg-blue-50 border-blue-200 text-blue-700'; // Near Miss
  }
};

/**
 * Gets a formatted filename for the incident report
 * @param incident The incident to generate a filename for
 * @returns Formatted filename
 */
export const getReportFilename = (incident: Incident): string => {
  const dateStr = format(incident.incidentDate, 'yyyy-MM-dd');
  const idStr = incident.id.replace(/\s+/g, '-');
  return `incident-report-${idStr}-${dateStr}.pdf`;
};
