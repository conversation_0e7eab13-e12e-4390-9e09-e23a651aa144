
import { z } from "zod";

export const incidentFormSchema = z.object({
  // Step 1 - Incident Information
  incidentTitle: z.string().min(1, "Incident title is required"),
  incidentDate: z.date({
    required_error: "Incident date is required",
  }),
  incidentTime: z.string().min(1, "Incident time is required"),
  incidentType: z.string().min(1, "Incident type is required"),
  description: z.string().min(20, "Please provide at least 20 characters").max(1000, "Maximum 1000 characters"),

  // Location information
  locationCountry: z.string().min(1, "Country is required"),
  locationCity: z.string().min(1, "City is required"),
  locationBusinessUnit: z.string().min(1, "Business unit is required"),
  locationProject: z.string().min(1, "Project/DC Ops is required"),
  locationDetails: z.string().min(1, "Level and location details are required"),

  // Classification of Actual Injury
  isWorkRelated: z.boolean().nullable(),
  lossOfConsciousness: z.boolean().nullable(),
  isDangerousOccurrence: z.boolean().nullable(),
  injuryClassification: z.object({
    isFatality: z.boolean().nullable(),
    isPermanentDisability: z.boolean().nullable(),
    isLostTimeIncident: z.boolean().nullable(),
    isMedicalTreatment: z.boolean().nullable(),
    isFirstAid: z.boolean().nullable(),
  }),

  // Incident Reviewer
  incidentReviewer: z.string().min(1, "Incident reviewer is required"),

  // Optional fields from original form
  propertyDamage: z.boolean().default(false),
  propertyDamageDetails: z.string().optional(),
  incidentCategory: z.string().optional(),
  circumstances: z.string().optional(),
  workplaceActivity: z.string().optional(),
  riskCategories: z.array(z.string()).optional(),
  photos: z.array(z.any()).optional(),
  impactAssessment: z.object({
    injury: z.string().optional(),
    environmentalDamage: z.string().optional(),
    productionLoss: z.string().optional(),
    reputationalDamage: z.string().optional(),
  }).optional(),

  reportToAuthorities: z.boolean().default(false),
  authorityReportDetails: z.string().optional(),

  // Final confirmation
  confirmAccuracy: z.boolean().refine(val => val === true, {
    message: "You must confirm the accuracy of the report",
  }),
});

export type IncidentFormValues = z.infer<typeof incidentFormSchema>;
