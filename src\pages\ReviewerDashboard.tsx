import React, { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Eye } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useUser } from "@/contexts/UserContext";
import { useIncidents, Incident } from "@/contexts/IncidentContext";
import IncidentActionDialog from "@/components/IncidentActionDialog";
import IncidentInvestigationView from "@/components/IncidentInvestigationView";
import AllIncidentsTable from "@/components/AllIncidentsTable";
import DataTable from "@/components/DataTable";
import { format } from "date-fns";
import { defaultPath } from "@/lib/paths";

const ReviewerDashboard = () => {
  const { userName, userTitle } = useUser();
  const { incidents, updateIncident, getIncidentsRequiringAction } = useIncidents();
  const [selectedIncident, setSelectedIncident] = useState<Incident | null>(null);
  const [isActionDialogOpen, setIsActionDialogOpen] = useState(false);
  const [isInvestigationViewOpen, setIsInvestigationViewOpen] = useState(false);

  // Filter incidents that need review (My Actions)
  const myActionIncidents = getIncidentsRequiringAction(userName, 'reviewer');
  console.log("Reviewer userName:", userName);
  console.log("Reviewer My Action Incidents:", myActionIncidents);

  // Filter incidents that are under investigation
  const underInvestigationIncidents = incidents.filter(
    (incident) => incident.status === "investigation"
  );
  console.log("Under Investigation Incidents:", underInvestigationIncidents);
  console.log("All Incidents:", incidents);

  // Helper functions for handling incidents
  const handleAction = (incidentId: string) => {
    // Find the incident and open the action dialog
    const incident = incidents.find(inc => inc.id === incidentId);
    if (incident) {
      setSelectedIncident(incident);
      setIsActionDialogOpen(true);
    }
  };

  const handleView = (incident: Incident) => {
    setSelectedIncident(incident);
    setIsInvestigationViewOpen(true);
  };

  return (
    <div className="w-full p-4 md:p-6 lg:p-8 animate-in fade-in duration-500">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold">Reviewer Dashboard</h1>
          <p className="text-muted-foreground">Review and process incident reports</p>
        </div>
      </div>

      <div className="bg-card rounded-lg border shadow-sm overflow-hidden">
        <Tabs defaultValue="my-actions" className="w-full">
          <div className="bg-muted/30 border-b">
            <TabsList className="w-full justify-start h-14 bg-transparent p-0">
              <TabsTrigger value="my-actions" className="relative px-6 py-3 h-full rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-background data-[state=active]:shadow-none transition-all">
                My Actions <span className="ml-1 bg-amber-100 text-amber-800 rounded-full px-2 py-0.5 text-xs">{myActionIncidents.length}</span>
              </TabsTrigger>
              <TabsTrigger value="all-incidents" className="relative px-6 py-3 h-full rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-background data-[state=active]:shadow-none transition-all">
                All Incidents
              </TabsTrigger>
              <TabsTrigger value="under-investigation" className="relative px-6 py-3 h-full rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-background data-[state=active]:shadow-none transition-all">
                Under Investigation <span className="ml-1 bg-blue-100 text-blue-800 rounded-full px-2 py-0.5 text-xs">{underInvestigationIncidents.length}</span>
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="my-actions" className="animate-in fade-in-50 duration-300 p-6">
            {myActionIncidents.length > 0 ? (
              <DataTable
                data={myActionIncidents}
                onView={handleView}
                onEdit={handleAction}
                onDelete={() => {}}
                context="my-actions"
              />
            ) : (
              <div className="p-8 text-center border rounded-md">
                <h3 className="text-lg font-medium mb-2">No incidents pending review</h3>
                <p className="text-muted-foreground">All reported incidents have been reviewed.</p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="under-investigation" className="animate-in fade-in-50 duration-300 p-6">
            <div className="rounded-md border">
              {underInvestigationIncidents.length > 0 ? (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader className="bg-muted/50">
                      <TableRow>
                        <TableHead className="font-semibold">ID</TableHead>
                        <TableHead className="font-semibold">Description</TableHead>
                        <TableHead className="font-semibold">Reported By</TableHead>
                        <TableHead className="font-semibold">Date</TableHead>
                        <TableHead className="font-semibold">Status</TableHead>
                        <TableHead className="text-right font-semibold">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {underInvestigationIncidents.map((incident) => (
                        <TableRow key={incident.id} className="hover:bg-muted/50">
                          <TableCell className="font-medium">{incident.id}</TableCell>
                          <TableCell>{incident.description}</TableCell>
                          <TableCell>{incident.reportedBy}</TableCell>
                          <TableCell>{format(incident.incidentDate, "do MMM yyyy")}</TableCell>
                          <TableCell>
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              <AlertCircle className="w-3 h-3 mr-1" /> Under Investigation
                            </span>
                          </TableCell>
                          <TableCell className="text-right">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleView(incident)}
                            >
                              View Details
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <div className="p-8 text-center">
                  <h3 className="text-lg font-medium mb-2">No incidents under investigation</h3>
                  <p className="text-muted-foreground">There are currently no incidents being investigated.</p>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="all-incidents" className="animate-in fade-in-50 duration-300 p-6">
            <AllIncidentsTable
              data={incidents}
              onView={handleView}
              userRole="reviewer"
              handleInvestigation={(incidentId, status) => {
                const incident = incidents.find(inc => inc.id === incidentId);
                if (incident) {
                  handleView(incident);
                }
              }}
            />
          </TabsContent>
        </Tabs>
      </div>

      {/* Incident Action Dialog */}
      <IncidentActionDialog
        open={isActionDialogOpen}
        onOpenChange={setIsActionDialogOpen}
        incident={selectedIncident}
        userRole="reviewer"
        onActionComplete={(updatedIncident) => {
          console.log("Action completed in ReviewerDashboard:", updatedIncident);
          // Use the updateIncident function from the context to update the incident
          updateIncident(updatedIncident.id, updatedIncident);
          // Clear the selected incident
          setSelectedIncident(null);
          // Close the dialog
          setIsActionDialogOpen(false);
        }}
      />

      {/* Incident Investigation View */}
      <IncidentInvestigationView
        open={isInvestigationViewOpen}
        onOpenChange={setIsInvestigationViewOpen}
        incident={selectedIncident}
      />
    </div>
  );
};

export default ReviewerDashboard;
