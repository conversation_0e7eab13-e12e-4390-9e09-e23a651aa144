
import React from "react";
import { cn } from "@/lib/utils";
import { Check } from "lucide-react";

interface FormStepIndicatorProps {
  totalSteps: number;
  currentStep: number;
  steps: { title: string }[];
  onStepClick?: (step: number) => void;
  completedSteps?: number[]; // Array of step numbers that have all required fields filled
}

const FormStepIndicator: React.FC<FormStepIndicatorProps> = ({
  totalSteps,
  currentStep,
  steps,
  onStepClick,
  completedSteps = [],
}) => {
  return (
    <div className="w-full mb-6">
      {/* Mobile View - Simple Step X of Y */}
      <div className="md:hidden mb-2">
        <p className="text-sm font-medium text-muted-foreground">
          Step {currentStep} of {totalSteps}: {steps[currentStep - 1].title}
          {completedSteps.includes(currentStep) && (
            <span className="ml-2 text-green-600 inline-flex items-center">
              <Check size={14} className="mr-1" /> Complete
            </span>
          )}
        </p>
        <div className="mt-2 h-2 bg-muted rounded-full overflow-hidden">
          <div
            className={cn(
              "h-full transition-all duration-500 ease-in-out rounded-full",
              completedSteps.includes(currentStep) ? "bg-green-500" : "bg-primary"
            )}
            style={{ width: `${(currentStep / totalSteps) * 100}%` }}
          ></div>
        </div>
      </div>

      {/* Desktop View - Horizontal Steps */}
      <div className="hidden md:block">
        <div className="flex items-center justify-between border-b pb-4">
          {steps.map((step, index) => {
            const stepNumber = index + 1;
            const isActive = stepNumber === currentStep;
            const isCompleted = stepNumber < currentStep;
            const isFullyCompleted = completedSteps.includes(stepNumber);
            const isClickable = onStepClick && (isCompleted || stepNumber === currentStep + 1);

            return (
              <div
                key={step.title}
                className={cn(
                  "flex items-center gap-2 py-2 px-4 rounded-full transition-colors",
                  isClickable ? "cursor-pointer hover:bg-muted" : "",
                  isActive
                    ? isFullyCompleted ? "text-green-600 font-medium" : "text-primary font-medium"
                    : isCompleted
                      ? isFullyCompleted ? "text-green-600" : "text-muted-foreground"
                      : "text-muted-foreground/50"
                )}
                onClick={() => isClickable && onStepClick(stepNumber)}
              >
                <div
                  className={cn(
                    "flex h-8 w-8 items-center justify-center rounded-full border-2 transition-colors",
                    isActive
                      ? isFullyCompleted
                        ? "border-green-500 bg-green-500 text-white"
                        : "border-primary bg-primary text-primary-foreground"
                      : isCompleted
                        ? isFullyCompleted
                          ? "border-green-500 bg-green-500 text-white"
                          : "border-primary bg-primary text-primary-foreground"
                        : "border-muted bg-background text-muted-foreground"
                  )}
                >
                  {isFullyCompleted ? (
                    <Check size={14} className="text-white" />
                  ) : (
                    <span className="text-xs font-medium">{stepNumber}</span>
                  )}
                </div>
                <span className="text-sm">{step.title}</span>
                {isFullyCompleted && !isActive && (
                  <span className="ml-1 text-green-600 text-xs">(Complete)</span>
                )}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default FormStepIndicator;
